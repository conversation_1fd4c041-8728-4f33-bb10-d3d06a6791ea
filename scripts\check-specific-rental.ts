import { PrismaClient } from '@prisma/client';
import { formatDuration } from '../lib/utils/duration';

const prisma = new PrismaClient();

async function checkSpecificRental() {
  try {
    console.log('Checking specific rental...');
    
    // Find rental that ends with qr9yrb
    const rental = await prisma.rental.findFirst({
      where: {
        id: {
          endsWith: 'qr9yrb'
        }
      },
      include: {
        product: true,
        user: true
      }
    });

    if (!rental) {
      console.log('Rental not found');
      return;
    }

    console.log('Rental found:');
    console.log('=============');
    console.log(`ID: ${rental.id}`);
    console.log(`Duration (raw): "${rental.duration}"`);
    console.log(`Duration (formatted): "${formatDuration(rental.duration)}"`);
    console.log(`Status: ${rental.status}`);
    console.log(`Product: ${rental.product?.name}`);
    console.log(`User: ${rental.user?.name}`);
    console.log(`Created: ${rental.createdAt}`);
    console.log(`Start Date: ${rental.startDate}`);
    console.log(`Arrival Time: ${rental.arrivalTime}`);
    console.log(`Total Price: ${rental.totalPrice}`);

    // Test the formatDuration function specifically with this rental's duration
    console.log('\nTesting formatDuration with this rental:');
    console.log(`formatDuration("${rental.duration}") = "${formatDuration(rental.duration)}"`);

  } catch (error) {
    console.error('Error checking specific rental:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSpecificRental();

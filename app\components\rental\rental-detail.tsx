"use client";

import { useState } from "react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { formatRupiah } from "@/lib/utils/format";
import { formatDuration } from "@/lib/utils/duration";
import { Rental } from "@/lib/types/rental";

interface RentalDetailProps {
  rental: Rental;
  isAdmin?: boolean;
}

export function RentalDetail({ rental }: RentalDetailProps) {
  const [showComments, setShowComments] = useState(false);

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return "-";
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        console.error("Invalid date value:", date);
        return "-";
      }
      return format(dateObj, "dd MMMM yyyy", { locale: id });
    } catch (error) {
      console.error("Error formatting date:", error, "Date value:", date);
      return "-";
    }
  };

  const formatTime = (date: Date | string | null | undefined) => {
    if (!date) return "-";
    try {
      if (typeof date === 'string' && /^\d{1,2}:\d{2}$/.test(date)) {
        return date;
      }

      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        console.error("Invalid date value:", date);
        return "-";
      }
      return format(dateObj, "HH:mm", { locale: id });
    } catch (error) {
      console.error("Error formatting time:", error, "Date value:", date);
      return "-";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-500/20 text-yellow-700 border-yellow-500">Menunggu</Badge>;
      case "CONFIRMED":
        return <Badge variant="outline" className="bg-blue-500/20 text-blue-700 border-blue-500">Dikonfirmasi</Badge>;
      case "ACTIVE":
        return <Badge variant="outline" className="bg-green-500/20 text-green-700 border-green-500">Aktif</Badge>;
      case "COMPLETED":
        return <Badge variant="outline" className="bg-purple-500/20 text-purple-700 border-purple-500">Selesai</Badge>;
      case "CANCELLED":
        return <Badge variant="outline" className="bg-red-500/20 text-red-700 border-red-500">Dibatalkan</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
          <div>
            <CardTitle>Detail Penyewaan #{rental.id.slice(-6)}</CardTitle>
            <CardDescription>
              Dibuat pada {formatDate(rental.createdAt)} {formatTime(rental.createdAt)}
            </CardDescription>
          </div>
          <div>{getStatusBadge(rental.status)}</div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Informasi Produk</h3>
              <div className="mt-1">
                <p className="text-base font-medium">{rental.product?.name || "Genset"}</p>
                <p className="text-sm text-gray-500">{rental.product?.capacity} KVA</p>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Periode</h3>
              <div className="mt-1">
                <p className="text-base font-medium">
                  {formatDate(rental.startDate)}
                </p>
                <p className="text-sm">
                  Jam Kedatangan: {formatTime(rental.arrivalTime)}
                </p>
                <p className="text-sm">
                  Durasi: {formatDuration(rental.duration)}
                </p>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Lokasi</h3>
              <div className="mt-1">
                <p className="text-base font-medium">{rental.address}</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Harga</h3>
              <div className="mt-1">
                <p className="text-lg font-bold">{formatRupiah(rental.totalPrice || 0)}</p>
                <p className="text-sm text-gray-500">
                  Deposit 50%: {formatRupiah((rental.totalPrice || 0) * 0.5)}
                </p>
                {(rental.overtimeHours || 0) > 0 && (
                  <p className="text-sm text-red-500">
                    Overtime: {rental.overtimeHours || 0} jam ({formatRupiah(rental.overtimeFee || 0)})
                  </p>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Catatan</h3>
              <div className="mt-1">
                <p className="text-sm">{rental.notes || "-"}</p>
              </div>
            </div>

            {rental.comments && (
              <div>
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Komentar Admin</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowComments(!showComments)}
                  >
                    {showComments ? "Sembunyikan" : "Lihat"}
                  </Button>
                </div>
                {showComments && (
                  <div className="mt-1 p-3 bg-gray-100 dark:bg-gray-800 rounded-md">
                    <p className="text-sm">{rental.comments}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 

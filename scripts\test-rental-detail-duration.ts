import { formatDuration } from '../lib/utils/duration';

// Test data yang menyimulasikan rental dari database
const testRentals = [
  { id: '1', duration: '8_JAM' },
  { id: '2', duration: '16_JAM' },
  { id: '3', duration: '24_JAM' },
  { id: '4', duration: '40_JAM' },
  { id: '5', duration: '1x8_HOURS' }, // Format lama
  { id: '6', duration: '2x8_HOURS' }, // Format lama
  { id: '7', duration: null },
  { id: '8', duration: '' },
];

console.log('Testing RentalDetail duration display:');
console.log('=====================================');

testRentals.forEach(rental => {
  const formattedDuration = formatDuration(rental.duration);
  console.log(`Rental ${rental.id}: "${rental.duration}" -> "${formattedDuration}"`);
});

console.log('\nExpected results:');
console.log('- 8_JAM should display as "8 Jam"');
console.log('- 16_J<PERSON> should display as "16 Jam"');
console.log('- 24_JAM should display as "24 Jam"');
console.log('- 40_JAM should display as "40 Jam"');
console.log('- 1x8_HOURS should display as "8 Jam" (backward compatibility)');
console.log('- 2x8_HOURS should display as "16 Jam" (backward compatibility)');
console.log('- null should display as "-"');
console.log('- empty string should display as "-"');

import { formatDuration } from '../lib/utils/duration';

// Test data yang menyimulasikan rental dari database
const testRentals = [
  { id: 'cmbhx12180001tm60i3qr9yrb', duration: '8_JAM' },
  { id: 'test2', duration: '16_JAM' },
  { id: 'test3', duration: '24_JAM' },
  { id: 'test4', duration: '40_JAM' },
  { id: 'test5', duration: '1x8_HOURS' }, // Format lama
  { id: 'test6', duration: '2x8_HOURS' }, // Format lama
  { id: 'test7', duration: null },
  { id: 'test8', duration: '' },
];

console.log('Testing ALL duration displays across components:');
console.log('===============================================');

console.log('\n1. RentalDetail component (rental-detail.tsx):');
console.log('   Durasi: {formatDuration(rental.duration)}');
testRentals.forEach(rental => {
  const formattedDuration = formatDuration(rental.duration);
  console.log(`   Rental ${rental.id.slice(-8)}: "${rental.duration}" -> "Durasi: ${formattedDuration}"`);
});

console.log('\n2. User Rentals page (user/rentals/page.tsx):');
console.log('   Durasi: {formatDuration(rental.duration)}');
testRentals.forEach(rental => {
  const formattedDuration = formatDuration(rental.duration);
  console.log(`   Rental ${rental.id.slice(-8)}: "${rental.duration}" -> "Durasi: ${formattedDuration}"`);
});

console.log('\n3. Admin Rentals table (admin/rentals-table.tsx):');
console.log('   {formatDuration(rental.duration)}');
testRentals.forEach(rental => {
  const formattedDuration = formatDuration(rental.duration);
  console.log(`   Rental ${rental.id.slice(-8)}: "${rental.duration}" -> "${formattedDuration}"`);
});

console.log('\n4. User Operations pages:');
console.log('   {formatDuration(rental.duration)}');
testRentals.forEach(rental => {
  const formattedDuration = formatDuration(rental.duration);
  console.log(`   Rental ${rental.id.slice(-8)}: "${rental.duration}" -> "${formattedDuration}"`);
});

console.log('\n✅ Expected results for rental cmbhx12180001tm60i3qr9yrb:');
console.log('   - Duration in DB: "8_JAM"');
console.log('   - Should display as: "8 Jam"');
console.log('   - NOT: "0 jam" or any other value');

console.log('\n🔧 If still showing "0 jam", possible causes:');
console.log('   1. Browser cache - try hard refresh (Ctrl+F5)');
console.log('   2. Component not using formatDuration() function');
console.log('   3. Data not being passed correctly to component');
console.log('   4. Component using old calculation method');

console.log('\n📝 All components should now use formatDuration() from lib/utils/duration.ts');

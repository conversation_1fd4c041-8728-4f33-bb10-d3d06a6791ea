import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkRentalDurationData() {
  try {
    console.log('Checking rental duration data...');
    
    // Get all rentals with their duration
    const rentals = await prisma.rental.findMany({
      select: {
        id: true,
        duration: true,
        status: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10 // Get latest 10 rentals
    });

    console.log(`Found ${rentals.length} rentals:`);
    console.log('================================');
    
    rentals.forEach((rental, index) => {
      console.log(`${index + 1}. ID: ${rental.id.slice(-8)}`);
      console.log(`   Duration: "${rental.duration}"`);
      console.log(`   Status: ${rental.status}`);
      console.log(`   Created: ${rental.createdAt.toISOString().split('T')[0]}`);
      console.log('');
    });

    // Check for any rentals with null or empty duration
    const rentalsWithNullDuration = await prisma.rental.count({
      where: {
        OR: [
          { duration: null },
          { duration: '' }
        ]
      }
    });

    console.log(`Rentals with null/empty duration: ${rentalsWithNullDuration}`);

    // Check duration distribution
    const durationDistribution = await prisma.rental.groupBy({
      by: ['duration'],
      _count: {
        duration: true
      }
    });

    console.log('\nDuration distribution:');
    console.log('=====================');
    durationDistribution.forEach(item => {
      console.log(`${item.duration || 'NULL'}: ${item._count.duration} rentals`);
    });

  } catch (error) {
    console.error('Error checking rental duration data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRentalDurationData();
